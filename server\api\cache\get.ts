// import { createStorage } from 'unstorage'
// import redisDriver from 'unstorage/drivers/redis'
import Redis from 'ioredis'

export default defineEventHandler(async () => {
	try {
		const redis = new Redis.Cluster([
			{
				host: 'v2-8e6rqo.serverless.euw1.cache.amazonaws.com',
				port: 6379,
			},
		], {
			redisOptions: {
				tls: {
					servername: 'v2-8e6rqo.serverless.euw1.cache.amazonaws.com',
				},
				db: 1,
			},
		})

		const nodes = redis.nodes('master') // get master nodes
		console.log(`Found ${nodes.length} master nodes`)

		const keys: string[] = []

		for (const node of nodes) {
			console.log(`Scanning node...`)
			let cursor = '0'
			do {
				const [nextCursor, foundKeys] = await node.scan(cursor, 'MATCH', '*', 'COUNT', 100)
				cursor = nextCursor
				console.log(`Scan result: cursor=${nextCursor}, foundKeys=${foundKeys.length}`)

				if (foundKeys.length > 0) {
					console.log(`Found keys:`, foundKeys)
					keys.push(...foundKeys)
				}
			} while (cursor !== '0')
		}

		console.log(`Total keys found: ${keys.length}`)

		return {
			message: 'Cache keys',
			keys,
		}
	} catch (error) {
		return {
			message: 'Failed to get cache keys',
			error: `${error}`,
		}
	}
	// get all cache keys
	/* try {
		const redis = createStorage({
			driver: redisDriver({
				base: '{unstorage}',

				cluster: [
					{
						host: 'v2-8e6rqo.serverless.euw1.cache.amazonaws.com',
						port: 6379,
					},
				],
				clusterOptions: {

					redisOptions: {

						tls: {
							servername: 'v2-8e6rqo.serverless.euw1.cache.amazonaws.com',
						},
					},
				},

			}),
		})

		const keys = await redis.getKeys('#')

		return {
			message: 'Cache keys',
			keys,
		}
	} catch (error) {
		return {
			message: 'Failed to get cache keys',
			error: `${error}`,
		}
	} */
})
